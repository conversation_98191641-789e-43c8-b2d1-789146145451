
import React, { useEffect, useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { User, LoginScreenConfig, LatestFeaturesConfig } from '../types';
import CheckCircleIcon from '../components/icons/CheckCircleIcon';
import XCircleIcon from '../components/icons/XCircleIcon';
import TrashIcon from '../components/icons/TrashIcon';
import AdminInbox from '../components/AdminInbox';
import ProfileEditModal from '../components/ProfileEditModal';
// import UserCircleIcon from '../components/icons/UserCircleIcon'; // Not currently used

const AdminPage: React.FC = () => {
  const { users, isAdmin, toggleUserActivation, removeUser, currentUser, loginScreenConfig, updateLoginScreenConfig, latestFeaturesConfig, updateLatestFeaturesConfig, migrateUsersForFollowSystem } = useAuth();
  const navigate = useNavigate();

  const [localLoginConfig, setLocalLoginConfig] = useState<LoginScreenConfig>(loginScreenConfig);
  const [localFeaturesConfig, setLocalFeaturesConfig] = useState<LatestFeaturesConfig>(latestFeaturesConfig);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [migrationLoading, setMigrationLoading] = useState(false);

  useEffect(() => {
    if (!isAdmin) {
      navigate('/');
    }
  }, [isAdmin, navigate]);

  useEffect(() => {
    setLocalLoginConfig(loginScreenConfig); // Sync with context if it changes elsewhere
  }, [loginScreenConfig]);

  useEffect(() => {
    setLocalFeaturesConfig(latestFeaturesConfig); // Sync with context if it changes elsewhere
  }, [latestFeaturesConfig]);

  if (!isAdmin) {
    return null;
  }

  const handleToggleActivation = (userId: string, currentStatus: boolean) => {
     const action = currentStatus ? "deactivate" : "activate";
     if (window.confirm(`Are you sure you want to ${action} this user?`)) {
       toggleUserActivation(userId);
     }
  };

  const handleRemove = (userId: string) => {
    if (window.confirm("Are you sure you want to PERMANENTLY remove this user? This action cannot be undone.")) {
      removeUser(userId);
    }
  };

  const getUserStatus = (user: User): string => {
    if (!user.isActive) return "Inactive";
    return "Active";
  };

  const handleLoginConfigChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setLocalLoginConfig(prev => ({
        ...prev,
        [name]: type === 'number' ? parseFloat(value) : value,
    }));
  };

  const handleLoginConfigSave = () => {
    updateLoginScreenConfig(localLoginConfig);
    alert("Login screen settings saved!");
  };

  const handleFeaturesConfigChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setLocalFeaturesConfig(prev => ({
        ...prev,
        [name]: checked,
      }));
    } else {
      setLocalFeaturesConfig(prev => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleFeaturesConfigSave = () => {
    updateLatestFeaturesConfig(localFeaturesConfig);
    alert("Latest Features settings saved!");
  };

  const handleMigrateUsers = async () => {
    if (!window.confirm("This will add followers/following arrays to existing users who don't have them. Continue?")) {
      return;
    }

    setMigrationLoading(true);
    try {
      const result = await migrateUsersForFollowSystem();
      alert(`Migration complete! Updated ${result.updated} out of ${result.total} users.`);
    } catch (error) {
      console.error('Migration error:', error);
      alert('Migration failed. Please check the console for details.');
    } finally {
      setMigrationLoading(false);
    }
  };


  return (
    <div className="max-w-6xl mx-auto py-8 px-2 sm:px-4 lg:px-6">
      <div className="flex flex-col sm:flex-row justify-between items-center mb-8 animate-slide-up">
        <h1 className="text-3xl font-bold text-brand-primary text-center sm:text-left animate-text-glow hover-text-glow">Admin Panel</h1>
        <Link
            to="/admin/analytics"
            className="mt-4 sm:mt-0 bg-brand-secondary hover:bg-brand-primary text-white font-semibold py-2 px-4 rounded-md transition-all duration-200 hover-scale hover-glow animate-cyber-flicker"
        >
            View Analytics
        </Link>
      </div>

      {/* Login Screen Customization */}
      <section className="mb-12 bg-neutral-surface shadow-xl rounded-lg border border-neutral-border p-6 animate-slide-up hover-glow cyber-border" style={{ animationDelay: '0.1s' }}>
        <h2 className="text-2xl font-semibold text-neutral-100 mb-6 border-b border-neutral-border pb-3 hover-text-glow transition-all duration-300">Login Screen Customization</h2>
        <div className="space-y-4">
          <div>
            <label htmlFor="loginMessage" className="block text-sm font-medium text-neutral-300 mb-1">Login Screen Message</label>
            <textarea
              id="loginMessage"
              name="message"
              rows={3}
              value={localLoginConfig.message}
              onChange={handleLoginConfigChange}
              className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
            />
          </div>
          <div>
            <label htmlFor="loginImageUrl" className="block text-sm font-medium text-neutral-300 mb-1">Login Screen Background Image URL</label>
            <input
              type="url"
              id="loginImageUrl"
              name="imageUrl"
              value={localLoginConfig.imageUrl}
              onChange={handleLoginConfigChange}
              className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
            />
          </div>
          <div>
            <label htmlFor="loginImageOverlayOpacity" className="block text-sm font-medium text-neutral-300 mb-1">
                Background Image Overlay Opacity (0 to 1, e.g., 0.5)
            </label>
            <input
              type="number"
              id="loginImageOverlayOpacity"
              name="imageOverlayOpacity"
              value={localLoginConfig.imageOverlayOpacity ?? 0.5}
              onChange={handleLoginConfigChange}
              min="0"
              max="1"
              step="0.05"
              className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
            />
          </div>
          <button
            onClick={handleLoginConfigSave}
            className="bg-brand-primary hover:bg-brand-secondary text-white font-semibold py-2 px-4 rounded-md transition-colors"
          >
            Save Login Screen Settings
          </button>
        </div>
      </section>

      {/* Latest Features Configuration */}
      <section className="mb-12 bg-neutral-surface shadow-xl rounded-lg border border-neutral-border p-6 animate-slide-up hover-glow cyber-border" style={{ animationDelay: '0.15s' }}>
        <h2 className="text-2xl font-semibold text-neutral-100 mb-6 border-b border-neutral-border pb-3 hover-text-glow transition-all duration-300">Latest Features Configuration</h2>
        <div className="space-y-4">
          <div>
            <label htmlFor="featuresEnabled" className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="featuresEnabled"
                name="enabled"
                checked={localFeaturesConfig.enabled}
                onChange={handleFeaturesConfigChange}
                className="w-4 h-4 text-brand-primary bg-neutral-base border-neutral-border rounded focus:ring-brand-primary focus:ring-2"
              />
              <span className="text-sm font-medium text-neutral-300">Enable Latest Features Section</span>
            </label>
          </div>
          <div>
            <label htmlFor="featuresTitle" className="block text-sm font-medium text-neutral-300 mb-1">
              Feature Title
            </label>
            <input
              type="text"
              id="featuresTitle"
              name="title"
              value={localFeaturesConfig.title}
              onChange={handleFeaturesConfigChange}
              placeholder="e.g., Video Embeds for your POSTS!"
              className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
            />
          </div>
          <div>
            <label htmlFor="featuresDescription" className="block text-sm font-medium text-neutral-300 mb-1">
              Feature Description
            </label>
            <textarea
              id="featuresDescription"
              name="description"
              value={localFeaturesConfig.description}
              onChange={handleFeaturesConfigChange}
              placeholder="e.g., Share videos directly in your posts with seamless embedding"
              rows={3}
              className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
            />
          </div>
          <div>
            <label htmlFor="featuresIcon" className="block text-sm font-medium text-neutral-300 mb-1">
              Feature Icon (emoji or single character)
            </label>
            <input
              type="text"
              id="featuresIcon"
              name="icon"
              value={localFeaturesConfig.icon}
              onChange={handleFeaturesConfigChange}
              placeholder="e.g., 🎥 or ✨"
              maxLength={2}
              className="w-full bg-neutral-base border border-neutral-border text-neutral-100 placeholder-neutral-muted px-3 py-2 rounded-md focus:ring-2 focus:ring-brand-primary focus:border-brand-primary"
            />
          </div>
          <button
            onClick={handleFeaturesConfigSave}
            className="bg-brand-primary hover:bg-brand-secondary text-white font-semibold py-2 px-4 rounded-md transition-colors"
          >
            Save Latest Features Settings
          </button>
        </div>
      </section>

      {/* Database Migration */}
      <section className="mb-12 bg-neutral-surface shadow-xl rounded-lg border border-neutral-border p-6 animate-slide-up hover-glow cyber-border" style={{ animationDelay: '0.2s' }}>
        <h2 className="text-2xl font-semibold text-neutral-100 mb-6 border-b border-neutral-border pb-3 hover-text-glow transition-all duration-300">Database Migration</h2>
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium text-neutral-200 mb-2">Follow System Migration</h3>
            <p className="text-sm text-neutral-300 mb-4">
              This will add empty followers and following arrays to existing users who don't have them.
              This is required for the new follow functionality to work properly.
            </p>
            <button
              onClick={handleMigrateUsers}
              disabled={migrationLoading}
              className="bg-accent-warning hover:bg-yellow-600 text-black font-semibold py-2 px-4 rounded-md transition-all duration-200 hover-scale hover-glow disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {migrationLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 border border-current border-t-transparent rounded-full animate-spin"></div>
                  <span>Migrating...</span>
                </div>
              ) : (
                'Migrate Users for Follow System'
              )}
            </button>
          </div>
        </div>
      </section>

      {/* User Management Table */}
      <section className="animate-slide-up" style={{ animationDelay: '0.3s' }}>
        <h2 className="text-2xl font-semibold text-neutral-100 mb-6 border-b border-neutral-border pb-3 hover-text-glow transition-all duration-300">User Management</h2>
        <div className="bg-neutral-surface shadow-xl rounded-lg border border-neutral-border overflow-x-auto hover-glow cyber-border">
          <table className="min-w-full divide-y divide-neutral-border">
            <thead className="bg-neutral-base">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-muted uppercase tracking-wider">User</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-muted uppercase tracking-wider">Status</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-muted uppercase tracking-wider min-w-[120px]">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-neutral-surface divide-y divide-neutral-border">
              {users.map((user) => (
                <tr key={user.id} className={`${user.id === currentUser?.id ? 'bg-brand-primary/5' : ''}`}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <img className="h-10 w-10 rounded-full border border-neutral-border" src={user.avatarUrl} alt={user.username} />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-neutral-100">{user.username}</div>
                        <div className="text-xs text-neutral-muted">{user.id}</div>
                        {user.bio && (
                          <div className="text-xs text-neutral-300 mt-1 line-clamp-2 max-w-xs">
                            {user.bio}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      user.isActive ? 'bg-accent-success/20 text-green-400' : 'bg-accent-error/20 text-red-400'
                    }`}>
                      {getUserStatus(user)}
                    </span>
                    {user.isAdmin && <span className="ml-2 text-xs text-red-400">(Admin)</span>}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-1">
                    {/* Edit Profile Button */}
                    <button
                      onClick={() => setEditingUser(user)}
                      title="Edit Profile"
                      className="text-brand-primary hover:text-brand-secondary p-1 rounded-md transition-all duration-200 hover-scale hover-glow"
                    >
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </button>

                    {/* Activate/Deactivate Button - only for non-admin users */}
                    {!user.isAdmin && (
                      <button
                        onClick={() => handleToggleActivation(user.id, user.isActive)}
                        title={user.isActive ? "Deactivate User" : "Activate User"}
                        className={`${user.isActive ? 'text-accent-warning hover:text-yellow-300' : 'text-accent-success hover:text-green-300'} p-1 rounded-md transition-all duration-200 hover-scale hover-glow`}
                      >
                        {user.isActive ? <XCircleIcon className="w-5 h-5" /> : <CheckCircleIcon className="w-5 h-5" />}
                      </button>
                    )}

                    {/* Remove Button - only for non-admin users */}
                    {!user.isAdmin && (
                      <button
                          onClick={() => handleRemove(user.id)}
                          title="Remove User"
                          className="text-accent-error hover:text-red-300 p-1 rounded-md transition-all duration-200 hover-scale animate-cyber-flicker"
                        >
                        <TrashIcon className="w-5 h-5" />
                      </button>
                    )}

                    {/* Admin indicator */}
                    {user.isAdmin && user.id !== currentUser?.id && <span className="text-xs text-neutral-muted italic">Admin User</span>}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        {users.length === 0 && <p className="text-center text-neutral-muted py-10">No users found.</p>}
      </section>

      {/* Admin Inbox Section */}
      <section className="mt-8 animate-slide-up" style={{ animationDelay: '0.4s' }}>
        <AdminInbox />
      </section>

      {/* Profile Edit Modal */}
      {editingUser && (
        <ProfileEditModal
          user={editingUser}
          onClose={() => setEditingUser(null)}
          isAdmin={true}
        />
      )}
    </div>
  );
};

export default AdminPage;
