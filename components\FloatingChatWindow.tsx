import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../hooks/useAuth';
import { Conversation, Message } from '../types';
import {
  subscribeToUserConversations,
  subscribeToConversationMessages,
  markMessagesAsRead,
  findOrCreateConversation
} from '../services/firebaseService';
import ConversationList from './ConversationList';
import MessageThread from './MessageThread';
import UserSearch from './UserSearch';

interface FloatingChatWindowProps {
  onClose: () => void;
}

const FloatingChatWindow: React.FC<FloatingChatWindowProps> = ({ onClose }) => {
  const { currentUser } = useAuth();
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [showUserSearch, setShowUserSearch] = useState(false);
  const [loading, setLoading] = useState(true);
  const windowRef = useRef<HTMLDivElement>(null);

  // Handle click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (windowRef.current && !windowRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onClose]);

  // Subscribe to conversations
  useEffect(() => {
    if (!currentUser) return;

    const unsubscribe = subscribeToUserConversations(currentUser.id, (updatedConversations) => {
      setConversations(updatedConversations);
      setLoading(false);
    });

    return () => unsubscribe();
  }, [currentUser]);

  // Subscribe to messages for selected conversation
  useEffect(() => {
    if (!selectedConversation) {
      setMessages([]);
      return;
    }

    const unsubscribe = subscribeToConversationMessages(
      selectedConversation.id,
      (updatedMessages) => {
        setMessages(updatedMessages);

        // Mark messages as read when viewing conversation
        if (currentUser && updatedMessages.length > 0) {
          markMessagesAsRead(selectedConversation.id, currentUser.id);
        }
      }
    );

    return () => unsubscribe();
  }, [selectedConversation, currentUser]);

  const handleSelectConversation = (conversation: Conversation) => {
    setSelectedConversation(conversation);
  };

  const handleStartNewConversation = async (userId: string) => {
    if (!currentUser) return;

    try {
      const conversationId = await findOrCreateConversation(currentUser.id, userId);
      const conversation = conversations.find(c => c.id === conversationId);
      if (conversation) {
        setSelectedConversation(conversation);
      }
      setShowUserSearch(false);
    } catch (error) {
      console.error('Error starting conversation:', error);
    }
  };

  const handleConversationDeleted = (conversationId: string) => {
    setConversations(prev => prev.filter(c => c.id !== conversationId));
    if (selectedConversation?.id === conversationId) {
      setSelectedConversation(null);
    }
  };

  const getTotalUnreadCount = () => {
    if (!currentUser) return 0;
    return conversations.reduce((total, conversation) => {
      const unreadForUser = conversation.unreadCount?.[currentUser.id] || 0;
      return total + unreadForUser;
    }, 0);
  };

  if (!currentUser) return null;

  return (
    <div
      ref={windowRef}
      className="fixed bottom-20 sm:bottom-24 right-4 sm:right-6 w-80 sm:w-96 h-96 sm:h-[500px] bg-neutral-surface rounded-lg border border-neutral-border cyber-border shadow-2xl z-40 flex flex-col animate-scale-in"
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-neutral-border bg-neutral-base rounded-t-lg">
        <h3 className="text-lg font-bold text-neutral-100 animate-text-glow">
          Messages
          {getTotalUnreadCount() > 0 && (
            <span className="ml-2 bg-brand-primary text-white text-xs rounded-full px-2 py-1 animate-pulse-glow">
              {getTotalUnreadCount()}
            </span>
          )}
        </h3>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowUserSearch(true)}
            className="bg-brand-primary hover:bg-brand-secondary text-white px-2 py-1 rounded text-xs transition-all duration-200 hover-scale hover-glow"
            title="New Chat"
          >
            +
          </button>
          <button
            onClick={onClose}
            className="text-neutral-muted hover:text-neutral-100 transition-all duration-200 hover-scale"
            title="Close"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 flex overflow-hidden">
        {!selectedConversation ? (
          /* Conversations List */
          <div className="w-full flex flex-col">
            {loading ? (
              <div className="flex-1 flex items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary"></div>
              </div>
            ) : conversations.length === 0 ? (
              <div className="flex-1 flex items-center justify-center p-4">
                <div className="text-center">
                  <div className="text-4xl mb-2 text-neutral-muted">💬</div>
                  <p className="text-neutral-muted text-sm mb-3">No conversations yet</p>
                  <button
                    onClick={() => setShowUserSearch(true)}
                    className="bg-brand-primary hover:bg-brand-secondary text-white px-3 py-2 rounded text-sm transition-all duration-200 hover-scale hover-glow"
                  >
                    Start Chat
                  </button>
                </div>
              </div>
            ) : (
              <div className="flex-1 overflow-y-auto">
                <ConversationList
                  conversations={conversations}
                  selectedConversation={selectedConversation}
                  onSelectConversation={handleSelectConversation}
                  currentUserId={currentUser.id}
                  onConversationDeleted={handleConversationDeleted}
                />
              </div>
            )}
          </div>
        ) : (
          /* Message Thread */
          <div className="w-full flex flex-col">
            <MessageThread
              conversation={selectedConversation}
              messages={messages}
              currentUserId={currentUser.id}
              onBackToList={() => setSelectedConversation(null)}
              isFloatingWindow={true}
            />
          </div>
        )}
      </div>

      {/* User Search Modal */}
      {showUserSearch && (
        <UserSearch
          onSelectUser={handleStartNewConversation}
          onClose={() => setShowUserSearch(false)}
          currentUserId={currentUser.id}
        />
      )}
    </div>
  );
};

export default FloatingChatWindow;
