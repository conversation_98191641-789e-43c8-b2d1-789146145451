import React, { useState, useEffect } from 'react';
import { useAuth } from '../hooks/useAuth';
import { getTotalUnreadCount } from '../services/firebaseService';
import MessagesIcon from './icons/MessagesIcon';
import FloatingChatWindow from './FloatingChatWindow';

const FloatingChatButton: React.FC = () => {
  const { currentUser } = useAuth();
  const [unreadCount, setUnreadCount] = useState(0);
  const [isChatOpen, setIsChatOpen] = useState(false);

  // Check for unread messages periodically
  useEffect(() => {
    if (!currentUser) {
      setUnreadCount(0);
      return;
    }

    const updateUnreadCount = async () => {
      try {
        const count = await getTotalUnreadCount();
        setUnreadCount(count);
      } catch (error) {
        console.error('Error fetching unread count:', error);
      }
    };

    // Initial load
    updateUnreadCount();

    // Update every 30 seconds
    const interval = setInterval(updateUnreadCount, 30000);

    return () => clearInterval(interval);
  }, [currentUser]);

  const handleToggleChat = () => {
    setIsChatOpen(!isChatOpen);
  };

  const handleCloseChat = () => {
    setIsChatOpen(false);
  };

  // Don't show if user is not logged in
  if (!currentUser) {
    return null;
  }

  return (
    <>
      <button
        onClick={handleToggleChat}
        className={`fixed bottom-4 sm:bottom-6 right-4 sm:right-6 z-50 bg-brand-primary hover:bg-brand-secondary text-white p-3 sm:p-4 rounded-full shadow-2xl transition-all duration-300 hover-scale hover-glow group mobile-touch-target ${unreadCount > 0 ? 'animate-pulse-glow' : ''}`}
        title="Messages"
      >
      <div className="relative">
        <MessagesIcon className="w-5 h-5 sm:w-6 sm:h-6" />
        {unreadCount > 0 && (
          <div className="absolute -top-1 sm:-top-2 -right-1 sm:-right-2 bg-red-500 text-white text-xs rounded-full min-w-[18px] sm:min-w-[20px] h-4 sm:h-5 flex items-center justify-center px-1 animate-pulse border-2 border-white">
            {unreadCount > 99 ? '99+' : unreadCount}
          </div>
        )}
      </div>

        {/* Tooltip - Hidden on mobile */}
        <div className="hidden sm:block absolute bottom-full right-0 mb-2 px-3 py-1 bg-neutral-surface border border-neutral-border rounded-md text-sm text-neutral-100 opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
          Messages {unreadCount > 0 && `(${unreadCount})`}
        </div>
      </button>

      {isChatOpen && (
        <FloatingChatWindow onClose={handleCloseChat} />
      )}
    </>
  );
};

export default FloatingChatButton;
