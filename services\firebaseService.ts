import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  User as FirebaseUser,
  updateProfile
} from 'firebase/auth';
import {
  doc,
  setDoc,
  getDoc,
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  orderBy,
  getDocs,
  where,
  Timestamp,
  onSnapshot,
  limit,
  arrayUnion,
  arrayRemove,
  serverTimestamp
} from 'firebase/firestore';
import {
  ref,
  uploadBytes,
  getDownloadURL,
  deleteObject
} from 'firebase/storage';
import { auth, db, storage } from '../firebase';
import { User, Post, Comment, AdminMessage, Message, Conversation, ConversationParticipant, MessageStatus } from '../types';

// Auth functions
export const signUp = async (email: string, password: string, username: string): Promise<User | null> => {
  try {
    // Create Firebase Auth user first
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const firebaseUser = userCredential.user;

    // Update the user's display name
    await updateProfile(firebaseUser, { displayName: username });

    // Create user document in Firestore
    const userData: User = {
      id: firebaseUser.uid,
      username,
      avatarUrl: `https://picsum.photos/seed/${username.replace(/\s+/g, '')}/100/100`,
      isActive: true, // Users are active immediately
      isPendingApproval: false, // No approval needed
      bio: '',
      followers: [],
      following: []
    };

    await setDoc(doc(db, 'users', firebaseUser.uid), userData);

    return userData;
  } catch (error) {
    console.error('Error signing up:', error);
    // If it's a username conflict, provide a clearer error message
    if (error instanceof Error && error.message.includes('already-exists')) {
      throw new Error('An account with this email already exists.');
    }
    throw error;
  }
};

export const signIn = async (email: string, password: string): Promise<FirebaseUser> => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const firebaseUser = userCredential.user;

    // Check if user is deactivated (but allow active users through)
    const userData = await getUserById(firebaseUser.uid);
    if (userData && !userData.isActive) {
      // Sign out the user immediately if they're deactivated
      await signOut(auth);
      throw new Error('Your account has been deactivated. Please contact an administrator.');
    }

    return firebaseUser;
  } catch (error) {
    console.error('Error signing in:', error);
    throw error;
  }
};

export const logOut = async (): Promise<void> => {
  try {
    await signOut(auth);
  } catch (error) {
    console.error('Error signing out:', error);
    throw error;
  }
};

// User functions
export const getUserById = async (userId: string): Promise<User | null> => {
  try {
    const userDoc = await getDoc(doc(db, 'users', userId));
    if (userDoc.exists()) {
      return userDoc.data() as User;
    }
    return null;
  } catch (error) {
    console.error('Error getting user:', error);
    throw error;
  }
};

export const updateUser = async (userId: string, userData: Partial<User>): Promise<void> => {
  try {
    await updateDoc(doc(db, 'users', userId), userData);
  } catch (error) {
    console.error('Error updating user:', error);
    throw error;
  }
};

export const deleteUser = async (userId: string): Promise<void> => {
  try {
    // Delete user document from Firestore
    await deleteDoc(doc(db, 'users', userId));

    // Clean up user's posts
    const postsQuery = query(collection(db, 'posts'), where('userId', '==', userId));
    const postsSnapshot = await getDocs(postsQuery);
    const deletePostPromises = postsSnapshot.docs.map(postDoc => deleteDoc(postDoc.ref));
    await Promise.all(deletePostPromises);

    // Clean up user's comments (remove from all posts)
    const allPostsQuery = query(collection(db, 'posts'));
    const allPostsSnapshot = await getDocs(allPostsQuery);
    const updatePostPromises = allPostsSnapshot.docs.map(async (postDoc) => {
      const postData = postDoc.data();
      if (postData.comments && postData.comments.length > 0) {
        const filteredComments = postData.comments.filter((comment: any) => comment.userId !== userId);
        if (filteredComments.length !== postData.comments.length) {
          await updateDoc(postDoc.ref, { comments: filteredComments });
        }
      }
    });
    await Promise.all(updatePostPromises);

    // Clean up user's conversations and messages
    const conversationsQuery = query(collection(db, 'conversations'), where('participants', 'array-contains', userId));
    const conversationsSnapshot = await getDocs(conversationsQuery);
    const deleteConversationPromises = conversationsSnapshot.docs.map(async (conversationDoc) => {
      const conversationData = conversationDoc.data();
      if (conversationData.participants.length === 2) {
        // If it's a 1-on-1 conversation, delete the entire conversation
        await cleanupConversation(conversationDoc.id);
      } else {
        // If it's a group conversation, just remove the user from participants
        const updatedParticipants = conversationData.participants.filter((id: string) => id !== userId);
        await updateDoc(conversationDoc.ref, { participants: updatedParticipants });
      }
    });
    await Promise.all(deleteConversationPromises);

    // Clean up follow relationships - remove this user from other users' following/followers lists
    const allUsersQuery = query(collection(db, 'users'));
    const allUsersSnapshot = await getDocs(allUsersQuery);
    const updateFollowPromises = allUsersSnapshot.docs.map(async (userDoc) => {
      const userData = userDoc.data();
      let needsUpdate = false;
      const updates: any = {};

      if (userData.followers && userData.followers.includes(userId)) {
        updates.followers = arrayRemove(userId);
        needsUpdate = true;
      }

      if (userData.following && userData.following.includes(userId)) {
        updates.following = arrayRemove(userId);
        needsUpdate = true;
      }

      if (needsUpdate) {
        await updateDoc(userDoc.ref, updates);
      }
    });
    await Promise.all(updateFollowPromises);

    console.log('User and associated data deleted successfully from Firestore:', userId);
  } catch (error) {
    console.error('Error deleting user:', error);
    throw error;
  }
};

export const getAllUsers = async (): Promise<User[]> => {
  try {
    const usersQuery = query(collection(db, 'users'));
    const querySnapshot = await getDocs(usersQuery);
    return querySnapshot.docs.map(doc => doc.data() as User);
  } catch (error) {
    console.error('Error getting users:', error);
    throw error;
  }
};

export const checkUsernameExists = async (username: string): Promise<boolean> => {
  try {
    const usersQuery = query(
      collection(db, 'users'),
      where('username', '==', username)
    );
    const querySnapshot = await getDocs(usersQuery);
    return !querySnapshot.empty;
  } catch (error) {
    console.error('Error checking username:', error);
    throw error;
  }
};

export const getActiveUsers = async (): Promise<User[]> => {
  try {
    const usersQuery = query(
      collection(db, 'users'),
      where('isActive', '==', true)
    );
    const querySnapshot = await getDocs(usersQuery);
    return querySnapshot.docs.map(doc => doc.data() as User);
  } catch (error) {
    console.error('Error getting active users:', error);
    throw error;
  }
};

// Post functions
export const createPost = async (postData: Omit<Post, 'id' | 'timestamp' | 'likes' | 'comments'>): Promise<string> => {
  try {
    const post = {
      ...postData,
      timestamp: Timestamp.now().toDate().toISOString(),
      likes: 0,
      likedUsers: [], // Initialize empty array for liked users
      comments: []
    };

    const docRef = await addDoc(collection(db, 'posts'), post);
    console.log('Post created successfully with ID:', docRef.id);
    return docRef.id;
  } catch (error) {
    console.error('Error creating post:', error);
    throw error;
  }
};

export const getAllPosts = async (): Promise<Post[]> => {
  try {
    // Simple query that orders by timestamp descending
    const postsQuery = query(
      collection(db, 'posts'),
      orderBy('timestamp', 'desc')
    );
    const querySnapshot = await getDocs(postsQuery);

    // Filter out placeholder documents and map to Post objects
    return querySnapshot.docs
      .filter(doc => !doc.data()._placeholder) // Filter out placeholder docs
      .map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Post));
  } catch (error) {
    console.error('Error getting posts:', error);
    throw error;
  }
};

export const getPostById = async (postId: string): Promise<Post | null> => {
  try {
    const postDoc = await getDoc(doc(db, 'posts', postId));
    if (postDoc.exists()) {
      return {
        id: postDoc.id,
        ...postDoc.data()
      } as Post;
    }
    return null;
  } catch (error) {
    console.error('Error getting post:', error);
    throw error;
  }
};

export const updatePost = async (postId: string, postData: Partial<Post>): Promise<void> => {
  try {
    await updateDoc(doc(db, 'posts', postId), postData);
  } catch (error) {
    console.error('Error updating post:', error);
    throw error;
  }
};

export const deletePost = async (postId: string): Promise<void> => {
  try {
    await deleteDoc(doc(db, 'posts', postId));
  } catch (error) {
    console.error('Error deleting post:', error);
    throw error;
  }
};

export const likePost = async (postId: string, userId: string): Promise<void> => {
  try {
    const postRef = doc(db, 'posts', postId);
    const postDoc = await getDoc(postRef);

    if (postDoc.exists()) {
      const postData = postDoc.data() as Post;
      const likedUsers = postData.likedUsers || [];

      if (!likedUsers.includes(userId)) {
        await updateDoc(postRef, {
          likes: (postData.likes || 0) + 1,
          likedUsers: [...likedUsers, userId]
        });
      }
    }
  } catch (error) {
    console.error('Error liking post:', error);
    throw error;
  }
};

export const unlikePost = async (postId: string, userId: string): Promise<void> => {
  try {
    const postRef = doc(db, 'posts', postId);
    const postDoc = await getDoc(postRef);

    if (postDoc.exists()) {
      const postData = postDoc.data() as Post;
      const likedUsers = postData.likedUsers || [];

      if (likedUsers.includes(userId)) {
        await updateDoc(postRef, {
          likes: Math.max((postData.likes || 0) - 1, 0),
          likedUsers: likedUsers.filter(id => id !== userId)
        });
      }
    }
  } catch (error) {
    console.error('Error unliking post:', error);
    throw error;
  }
};

// Helper functions for comment threading
const findCommentDepth = (comments: Comment[], commentId: string): number => {
  for (const comment of comments) {
    if (comment.id === commentId) {
      return comment.depth || 0;
    }
    if (comment.replies && comment.replies.length > 0) {
      const depth = findCommentDepth(comment.replies, commentId);
      if (depth !== -1) return depth;
    }
  }
  return 0;
};

const addReplyToComment = (comments: Comment[], parentId: string, newReply: Comment): Comment[] => {
  return comments.map(comment => {
    if (comment.id === parentId) {
      return {
        ...comment,
        replies: [newReply, ...(comment.replies || [])]
      };
    }
    if (comment.replies && comment.replies.length > 0) {
      return {
        ...comment,
        replies: addReplyToComment(comment.replies, parentId, newReply)
      };
    }
    return comment;
  });
};

const removeCommentFromTree = (comments: Comment[], commentId: string): Comment[] => {
  return comments.filter(comment => {
    if (comment.id === commentId) {
      return false;
    }
    if (comment.replies && comment.replies.length > 0) {
      comment.replies = removeCommentFromTree(comment.replies, commentId);
    }
    return true;
  });
};

const updateCommentInTree = (comments: Comment[], commentId: string, updateFn: (comment: Comment) => Comment): Comment[] => {
  return comments.map(comment => {
    if (comment.id === commentId) {
      return updateFn(comment);
    }
    if (comment.replies && comment.replies.length > 0) {
      return {
        ...comment,
        replies: updateCommentInTree(comment.replies, commentId, updateFn)
      };
    }
    return comment;
  });
};

// Comment functions
export const addCommentToPost = async (postId: string, commentData: Omit<Comment, 'id' | 'timestamp'>): Promise<string> => {
  try {
    const postRef = doc(db, 'posts', postId);
    const postDoc = await getDoc(postRef);

    if (postDoc.exists()) {
      const postData = postDoc.data() as Post;
      const currentComments = postData.comments || [];

      const newComment: Comment = {
        id: `comment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId: commentData.userId,
        username: commentData.username,
        avatarUrl: commentData.avatarUrl,
        text: commentData.text,
        timestamp: Timestamp.now().toDate().toISOString(),
        likes: 0,
        likedUsers: [],
        depth: commentData.parentId ? (findCommentDepth(currentComments, commentData.parentId) + 1) : 0,
        replies: [],
        // Only include optional fields if they have values
        ...(commentData.parentId && { parentId: commentData.parentId }),
        ...(commentData.replyToUsername && { replyToUsername: commentData.replyToUsername }),
      };

      let updatedComments;
      if (commentData.parentId) {
        // This is a reply - add it to the parent comment's replies
        updatedComments = addReplyToComment(currentComments, commentData.parentId, newComment);
      } else {
        // This is a top-level comment
        updatedComments = [newComment, ...currentComments];
      }

      await updateDoc(postRef, {
        comments: updatedComments
      });

      console.log('Comment added successfully to post:', postId);
      return newComment.id;
    } else {
      throw new Error('Post not found');
    }
  } catch (error) {
    console.error('Error adding comment to post:', error);
    throw error;
  }
};

export const deleteCommentFromPost = async (postId: string, commentId: string): Promise<void> => {
  try {
    const postRef = doc(db, 'posts', postId);
    const postDoc = await getDoc(postRef);

    if (postDoc.exists()) {
      const postData = postDoc.data() as Post;
      const currentComments = postData.comments || [];

      const updatedComments = removeCommentFromTree(currentComments, commentId);

      await updateDoc(postRef, {
        comments: updatedComments
      });

      console.log('Comment deleted successfully from post:', postId);
    } else {
      throw new Error('Post not found');
    }
  } catch (error) {
    console.error('Error deleting comment from post:', error);
    throw error;
  }
};

export const likeComment = async (postId: string, commentId: string, userId: string): Promise<void> => {
  try {
    const postRef = doc(db, 'posts', postId);
    const postDoc = await getDoc(postRef);

    if (postDoc.exists()) {
      const postData = postDoc.data() as Post;
      const currentComments = postData.comments || [];

      const updatedComments = updateCommentInTree(currentComments, commentId, (comment) => {
        const likedUsers = comment.likedUsers || [];
        if (!likedUsers.includes(userId)) {
          return {
            ...comment,
            likes: (comment.likes || 0) + 1,
            likedUsers: [...likedUsers, userId]
          };
        }
        return comment;
      });

      await updateDoc(postRef, {
        comments: updatedComments
      });

      console.log('Comment liked successfully:', commentId);
    } else {
      throw new Error('Post not found');
    }
  } catch (error) {
    console.error('Error liking comment:', error);
    throw error;
  }
};

export const unlikeComment = async (postId: string, commentId: string, userId: string): Promise<void> => {
  try {
    const postRef = doc(db, 'posts', postId);
    const postDoc = await getDoc(postRef);

    if (postDoc.exists()) {
      const postData = postDoc.data() as Post;
      const currentComments = postData.comments || [];

      const updatedComments = updateCommentInTree(currentComments, commentId, (comment) => {
        const likedUsers = comment.likedUsers || [];
        if (likedUsers.includes(userId)) {
          return {
            ...comment,
            likes: Math.max((comment.likes || 0) - 1, 0),
            likedUsers: likedUsers.filter(id => id !== userId)
          };
        }
        return comment;
      });

      await updateDoc(postRef, {
        comments: updatedComments
      });

      console.log('Comment unliked successfully:', commentId);
    } else {
      throw new Error('Post not found');
    }
  } catch (error) {
    console.error('Error unliking comment:', error);
    throw error;
  }
};

// Get posts by user ID
export const getPostsByUserId = async (userId: string): Promise<Post[]> => {
  try {
    const postsQuery = query(
      collection(db, 'posts'),
      where('userId', '==', userId),
      orderBy('timestamp', 'desc')
    );
    const querySnapshot = await getDocs(postsQuery);

    return querySnapshot.docs
      .filter(doc => !doc.data()._placeholder)
      .map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Post));
  } catch (error) {
    console.error('Error getting posts by user ID:', error);
    throw error;
  }
};

// Get posts by tags
export const getPostsByTag = async (tag: string): Promise<Post[]> => {
  try {
    const postsQuery = query(
      collection(db, 'posts'),
      where('tags', 'array-contains', tag),
      orderBy('timestamp', 'desc')
    );
    const querySnapshot = await getDocs(postsQuery);

    return querySnapshot.docs
      .filter(doc => !doc.data()._placeholder)
      .map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Post));
  } catch (error) {
    console.error('Error getting posts by tag:', error);
    throw error;
  }
};

// Storage functions
export const uploadImage = async (file: File, path: string): Promise<string> => {
  try {
    const storageRef = ref(storage, path);
    const snapshot = await uploadBytes(storageRef, file);
    const downloadURL = await getDownloadURL(snapshot.ref);
    return downloadURL;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
};

export const deleteImage = async (path: string): Promise<void> => {
  try {
    const storageRef = ref(storage, path);
    await deleteObject(storageRef);
  } catch (error) {
    console.error('Error deleting image:', error);
    throw error;
  }
};

// Admin message functions
export const createAdminMessage = async (messageData: Omit<AdminMessage, 'id' | 'timestamp' | 'isRead'>): Promise<string> => {
  try {
    const message = {
      ...messageData,
      timestamp: Timestamp.now().toDate().toISOString(),
      isRead: false
    };

    const docRef = await addDoc(collection(db, 'adminMessages'), message);
    return docRef.id;
  } catch (error) {
    console.error('Error creating admin message:', error);
    throw error;
  }
};

export const getAllAdminMessages = async (): Promise<AdminMessage[]> => {
  try {
    const messagesQuery = query(
      collection(db, 'adminMessages'),
      where('_placeholder', '!=', true),
      orderBy('_placeholder'),
      orderBy('timestamp', 'desc')
    );
    const querySnapshot = await getDocs(messagesQuery);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as AdminMessage));
  } catch (error) {
    console.error('Error getting admin messages:', error);
    // Fallback query
    try {
      const fallbackQuery = query(collection(db, 'adminMessages'), orderBy('timestamp', 'desc'));
      const fallbackSnapshot = await getDocs(fallbackQuery);
      return fallbackSnapshot.docs
        .filter(doc => !doc.data()._placeholder)
        .map(doc => ({
          id: doc.id,
          ...doc.data()
        } as AdminMessage));
    } catch (fallbackError) {
      console.error('Fallback query also failed:', fallbackError);
      return [];
    }
  }
};

export const markAdminMessageAsRead = async (messageId: string): Promise<void> => {
  try {
    await updateDoc(doc(db, 'adminMessages', messageId), { isRead: true });
  } catch (error) {
    console.error('Error marking message as read:', error);
    throw error;
  }
};

export const deleteAdminMessage = async (messageId: string): Promise<void> => {
  try {
    await deleteDoc(doc(db, 'adminMessages', messageId));
  } catch (error) {
    console.error('Error deleting admin message:', error);
    throw error;
  }
};

// Login screen config functions
export const getLoginScreenConfig = async (): Promise<any> => {
  try {
    const configDoc = await getDoc(doc(db, 'config', 'loginScreen'));
    if (configDoc.exists()) {
      return configDoc.data();
    }
    return null;
  } catch (error) {
    console.error('Error getting login screen config:', error);
    throw error;
  }
};

export const updateLoginScreenConfig = async (config: any): Promise<void> => {
  try {
    await setDoc(doc(db, 'config', 'loginScreen'), config);
  } catch (error) {
    console.error('Error updating login screen config:', error);
    throw error;
  }
};

// Latest Features config functions
export const getLatestFeaturesConfig = async (): Promise<any> => {
  try {
    const configDoc = await getDoc(doc(db, 'config', 'latestFeatures'));
    if (configDoc.exists()) {
      return configDoc.data();
    }
    return null;
  } catch (error) {
    console.error('Error getting latest features config:', error);
    throw error;
  }
};

export const updateLatestFeaturesConfig = async (config: any): Promise<void> => {
  try {
    await setDoc(doc(db, 'config', 'latestFeatures'), config);
  } catch (error) {
    console.error('Error updating latest features config:', error);
    throw error;
  }
};

// Admin functions
export const createAdminUser = async (email: string, password: string, username: string): Promise<User> => {
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const firebaseUser = userCredential.user;

    // Update the user's display name
    await updateProfile(firebaseUser, { displayName: username });

    // Create admin user document in Firestore
    const userData: User = {
      id: firebaseUser.uid,
      username,
      avatarUrl: `https://picsum.photos/seed/${username.replace(/\s+/g, '')}/100/100`,
      isActive: true,
      isPendingApproval: false,
      isAdmin: true,
      bio: 'Platform Administrator\nGuardian of the digital realm\nMaintaining order in chaos\n⚠️ With great power...',
      followers: [],
      following: []
    };

    await setDoc(doc(db, 'users', firebaseUser.uid), userData);

    return userData;
  } catch (error) {
    console.error('Error creating admin user:', error);
    throw error;
  }
};

// Messaging System Functions

// Create a new conversation
export const createConversation = async (participantIds: string[]): Promise<string> => {
  try {
    // Get participant details
    const participantDetails: ConversationParticipant[] = [];
    for (const participantId of participantIds) {
      const user = await getUserById(participantId);
      if (user) {
        participantDetails.push({
          id: user.id,
          username: user.username,
          avatarUrl: user.avatarUrl,
          isActive: user.isActive
        });
      }
    }

    const conversation: Omit<Conversation, 'id'> = {
      participants: participantIds,
      participantDetails,
      lastActivity: new Date().toISOString(),
      unreadCount: participantIds.reduce((acc, id) => ({ ...acc, [id]: 0 }), {}),
      isArchived: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const docRef = await addDoc(collection(db, 'conversations'), conversation);
    console.log('Conversation created successfully with ID:', docRef.id);
    return docRef.id;
  } catch (error) {
    console.error('Error creating conversation:', error);
    throw error;
  }
};

// Get conversations for a user (filtered by user's deleted conversations)
export const getUserConversations = async (userId: string): Promise<Conversation[]> => {
  try {
    const conversationsQuery = query(
      collection(db, 'conversations'),
      where('participants', 'array-contains', userId),
      orderBy('lastActivity', 'desc')
    );
    const querySnapshot = await getDocs(conversationsQuery);

    return querySnapshot.docs
      .map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Conversation))
      .filter(conversation => {
        // Filter out conversations that this user has deleted
        const deletedBy = conversation.deletedBy || [];
        return !deletedBy.includes(userId);
      });
  } catch (error) {
    console.error('Error getting user conversations:', error);
    throw error;
  }
};

// Listen to conversations for real-time updates (filtered by user's deleted conversations)
export const subscribeToUserConversations = (
  userId: string,
  callback: (conversations: Conversation[]) => void
): (() => void) => {
  try {
    const conversationsQuery = query(
      collection(db, 'conversations'),
      where('participants', 'array-contains', userId),
      orderBy('lastActivity', 'desc')
    );

    const unsubscribe = onSnapshot(conversationsQuery, (querySnapshot) => {
      const conversations = querySnapshot.docs
        .map(doc => ({
          id: doc.id,
          ...doc.data()
        } as Conversation))
        .filter(conversation => {
          // Filter out conversations that this user has deleted
          const deletedBy = conversation.deletedBy || [];
          return !deletedBy.includes(userId);
        });
      callback(conversations);
    });

    return unsubscribe;
  } catch (error) {
    console.error('Error subscribing to conversations:', error);
    throw error;
  }
};

// Send a message
export const sendMessage = async (
  conversationId: string,
  senderId: string,
  content: string
): Promise<string> => {
  try {
    // Get sender details
    const sender = await getUserById(senderId);
    if (!sender) {
      throw new Error('Sender not found');
    }

    const message: Omit<Message, 'id'> = {
      conversationId,
      senderId,
      senderUsername: sender.username,
      senderAvatarUrl: sender.avatarUrl,
      content,
      timestamp: new Date().toISOString(),
      status: MessageStatus.SENT,
      isRead: false,
      readBy: []
    };

    // Add message to messages collection
    const messageRef = await addDoc(collection(db, 'messages'), message);

    // Update conversation with last message and activity
    const conversationRef = doc(db, 'conversations', conversationId);
    const conversationDoc = await getDoc(conversationRef);

    if (conversationDoc.exists()) {
      const conversationData = conversationDoc.data() as Conversation;
      const updatedUnreadCount = { ...conversationData.unreadCount };

      // Increment unread count for all participants except sender
      conversationData.participants.forEach(participantId => {
        if (participantId !== senderId) {
          updatedUnreadCount[participantId] = (updatedUnreadCount[participantId] || 0) + 1;
        }
      });

      await updateDoc(conversationRef, {
        lastMessage: { id: messageRef.id, ...message },
        lastActivity: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        unreadCount: updatedUnreadCount
      });
    }

    console.log('Message sent successfully with ID:', messageRef.id);
    return messageRef.id;
  } catch (error) {
    console.error('Error sending message:', error);
    throw error;
  }
};

// Get messages for a conversation
export const getConversationMessages = async (
  conversationId: string,
  limitCount: number = 50
): Promise<Message[]> => {
  try {
    const messagesQuery = query(
      collection(db, 'messages'),
      where('conversationId', '==', conversationId),
      orderBy('timestamp', 'desc'),
      limit(limitCount)
    );
    const querySnapshot = await getDocs(messagesQuery);

    return querySnapshot.docs
      .map(doc => ({
        id: doc.id,
        ...doc.data()
      } as Message))
      .reverse(); // Reverse to show oldest first
  } catch (error) {
    console.error('Error getting conversation messages:', error);
    throw error;
  }
};

// Listen to messages for real-time updates
export const subscribeToConversationMessages = (
  conversationId: string,
  callback: (messages: Message[]) => void,
  limitCount: number = 50
): (() => void) => {
  try {
    const messagesQuery = query(
      collection(db, 'messages'),
      where('conversationId', '==', conversationId),
      orderBy('timestamp', 'desc'),
      limit(limitCount)
    );

    const unsubscribe = onSnapshot(messagesQuery, (querySnapshot) => {
      const messages = querySnapshot.docs
        .map(doc => ({
          id: doc.id,
          ...doc.data()
        } as Message))
        .reverse(); // Reverse to show oldest first
      callback(messages);
    });

    return unsubscribe;
  } catch (error) {
    console.error('Error subscribing to messages:', error);
    throw error;
  }
};

// Mark messages as read
export const markMessagesAsRead = async (
  conversationId: string,
  userId: string
): Promise<void> => {
  try {
    // Get unread messages in the conversation
    const messagesQuery = query(
      collection(db, 'messages'),
      where('conversationId', '==', conversationId),
      where('senderId', '!=', userId),
      where('isRead', '==', false)
    );
    const querySnapshot = await getDocs(messagesQuery);

    // Update each message to mark as read
    const updatePromises = querySnapshot.docs.map(messageDoc => {
      const messageData = messageDoc.data() as Message;
      const readBy = messageData.readBy || [];

      if (!readBy.includes(userId)) {
        return updateDoc(doc(db, 'messages', messageDoc.id), {
          isRead: true,
          readBy: arrayUnion(userId)
        });
      }
      return Promise.resolve();
    });

    await Promise.all(updatePromises);

    // Reset unread count for this user in the conversation
    const conversationRef = doc(db, 'conversations', conversationId);
    const conversationDoc = await getDoc(conversationRef);

    if (conversationDoc.exists()) {
      const conversationData = conversationDoc.data() as Conversation;
      const updatedUnreadCount = { ...conversationData.unreadCount };
      updatedUnreadCount[userId] = 0;

      await updateDoc(conversationRef, {
        unreadCount: updatedUnreadCount
      });
    }

    console.log('Messages marked as read for user:', userId);
  } catch (error) {
    console.error('Error marking messages as read:', error);
    throw error;
  }
};

// Delete a conversation (for current user only - conversation remains for other participants)
export const deleteConversation = async (
  conversationId: string,
  userId: string
): Promise<void> => {
  try {
    const conversationDoc = await getDoc(doc(db, 'conversations', conversationId));

    if (!conversationDoc.exists()) {
      throw new Error('Conversation not found');
    }

    const conversationData = conversationDoc.data() as Conversation;

    // Verify user is a participant
    if (!conversationData.participants.includes(userId)) {
      throw new Error('You are not a participant in this conversation');
    }

    // Add the user to the deletedBy array
    const deletedBy = conversationData.deletedBy || [];

    if (!deletedBy.includes(userId)) {
      await updateDoc(doc(db, 'conversations', conversationId), {
        deletedBy: arrayUnion(userId)
      });

      // Check if all participants have deleted the conversation
      const updatedDeletedBy = [...deletedBy, userId];
      const allParticipantsDeleted = conversationData.participants.every(
        participantId => updatedDeletedBy.includes(participantId)
      );

      // If all participants have deleted it, completely remove the conversation and its messages
      if (allParticipantsDeleted) {
        await cleanupConversation(conversationId);
      }
    }

    console.log('Conversation deleted for user:', conversationId, userId);
  } catch (error) {
    console.error('Error deleting conversation:', error);
    throw error;
  }
};

// Helper function to completely remove a conversation and its messages
const cleanupConversation = async (conversationId: string): Promise<void> => {
  try {
    // Delete all messages in the conversation
    const messagesQuery = query(
      collection(db, 'messages'),
      where('conversationId', '==', conversationId)
    );
    const messagesSnapshot = await getDocs(messagesQuery);

    const deletePromises = messagesSnapshot.docs.map(messageDoc =>
      deleteDoc(doc(db, 'messages', messageDoc.id))
    );

    await Promise.all(deletePromises);

    // Delete the conversation itself
    await deleteDoc(doc(db, 'conversations', conversationId));

    console.log('Conversation and all messages completely removed:', conversationId);
  } catch (error) {
    console.error('Error cleaning up conversation:', error);
    throw error;
  }
};

// Follow a user
export const followUser = async (
  currentUserId: string,
  targetUserId: string
): Promise<void> => {
  try {
    if (currentUserId === targetUserId) {
      throw new Error('You cannot follow yourself');
    }

    // Add targetUserId to current user's following list
    await updateDoc(doc(db, 'users', currentUserId), {
      following: arrayUnion(targetUserId)
    });

    // Add currentUserId to target user's followers list
    await updateDoc(doc(db, 'users', targetUserId), {
      followers: arrayUnion(currentUserId)
    });

    console.log('User followed successfully:', targetUserId);
  } catch (error) {
    console.error('Error following user:', error);
    throw error;
  }
};

// Unfollow a user
export const unfollowUser = async (
  currentUserId: string,
  targetUserId: string
): Promise<void> => {
  try {
    if (currentUserId === targetUserId) {
      throw new Error('You cannot unfollow yourself');
    }

    // Remove targetUserId from current user's following list
    await updateDoc(doc(db, 'users', currentUserId), {
      following: arrayRemove(targetUserId)
    });

    // Remove currentUserId from target user's followers list
    await updateDoc(doc(db, 'users', targetUserId), {
      followers: arrayRemove(currentUserId)
    });

    console.log('User unfollowed successfully:', targetUserId);
  } catch (error) {
    console.error('Error unfollowing user:', error);
    throw error;
  }
};

// Check if current user is following target user
export const isFollowing = async (
  currentUserId: string,
  targetUserId: string
): Promise<boolean> => {
  try {
    const userDoc = await getDoc(doc(db, 'users', currentUserId));
    if (userDoc.exists()) {
      const userData = userDoc.data() as User;
      return userData.following?.includes(targetUserId) || false;
    }
    return false;
  } catch (error) {
    console.error('Error checking follow status:', error);
    return false;
  }
};

// Get total unread messages count for a user
export const getTotalUnreadCount = async (userId: string): Promise<number> => {
  try {
    const conversationsQuery = query(
      collection(db, 'conversations'),
      where('participants', 'array-contains', userId)
    );
    const querySnapshot = await getDocs(conversationsQuery);

    let totalUnread = 0;
    querySnapshot.docs.forEach(doc => {
      const conversation = doc.data() as Conversation;
      const unreadCount = conversation.unreadCount?.[userId] || 0;
      totalUnread += unreadCount;
    });

    return totalUnread;
  } catch (error) {
    console.error('Error getting total unread count:', error);
    return 0;
  }
};

// Admin function to migrate existing users to have followers/following arrays
export const migrateUsersForFollowSystem = async (): Promise<{ updated: number; total: number }> => {
  try {
    const usersQuery = query(collection(db, 'users'));
    const querySnapshot = await getDocs(usersQuery);

    let updatedCount = 0;
    const totalCount = querySnapshot.docs.length;

    console.log(`Found ${totalCount} users to check for migration...`);

    for (const userDoc of querySnapshot.docs) {
      const userData = userDoc.data() as User;
      const userId = userDoc.id;

      // Check if user already has followers and following arrays
      const needsUpdate = !userData.followers || !userData.following;

      if (needsUpdate) {
        const updateData: Partial<User> = {};

        if (!userData.followers) {
          updateData.followers = [];
        }

        if (!userData.following) {
          updateData.following = [];
        }

        await updateDoc(doc(db, 'users', userId), updateData);
        updatedCount++;

        console.log(`Updated user: ${userData.username} (${userId})`);
      }
    }

    console.log(`Migration complete! Updated ${updatedCount} out of ${totalCount} users.`);
    return { updated: updatedCount, total: totalCount };

  } catch (error) {
    console.error('Error during user migration:', error);
    throw error;
  }
};

// Find or create conversation between users
export const findOrCreateConversation = async (
  currentUserId: string,
  otherUserId: string
): Promise<string> => {
  try {
    // Check if conversation already exists between these users
    const conversationsQuery = query(
      collection(db, 'conversations'),
      where('participants', 'array-contains', currentUserId)
    );
    const querySnapshot = await getDocs(conversationsQuery);

    // Find existing conversation with the other user
    const existingConversation = querySnapshot.docs.find(doc => {
      const data = doc.data() as Conversation;
      return data.participants.includes(otherUserId) && data.participants.length === 2;
    });

    if (existingConversation) {
      return existingConversation.id;
    }

    // Create new conversation if none exists
    return await createConversation([currentUserId, otherUserId]);
  } catch (error) {
    console.error('Error finding or creating conversation:', error);
    throw error;
  }
};

// Search users for messaging
export const searchUsersForMessaging = async (
  searchTerm: string,
  currentUserId: string
): Promise<User[]> => {
  try {
    // Get all active users
    const usersQuery = query(
      collection(db, 'users'),
      where('isActive', '==', true)
    );
    const querySnapshot = await getDocs(usersQuery);

    const users = querySnapshot.docs
      .map(doc => doc.data() as User)
      .filter(user =>
        user.id !== currentUserId && // Exclude current user
        user.username.toLowerCase().includes(searchTerm.toLowerCase())
      );

    return users;
  } catch (error) {
    console.error('Error searching users for messaging:', error);
    throw error;
  }
};
