
import React, { createContext, useState, ReactNode, useMemo, useCallback, useEffect } from 'react';
import { User, LoginScreenConfig, LatestFeaturesConfig, AdminMessage } from '../types';
import { DEFAULT_LOGIN_SCREEN_CONFIG, DEFAULT_LATEST_FEATURES_CONFIG } from '../constants';
import { onAuthStateChanged, User as FirebaseUser } from 'firebase/auth';
import { auth } from '../firebase';
import {
  getUserById,
  signIn,
  signUp as firebaseSignUp,
  logOut,
  getAllUsers,
  getActiveUsers,
  updateUser,
  deleteUser,
  getLoginScreenConfig,
  updateLoginScreenConfig as firebaseUpdateLoginScreenConfig,
  getLatestFeaturesConfig,
  updateLatestFeaturesConfig as firebaseUpdateLatestFeaturesConfig,
  getAllAdminMessages,
  createAdminMessage,
  markAdminMessageAsRead,
  deleteAdminMessage as firebaseDeleteAdminMessage,
  migrateUsersForFollowSystem
} from '../services/firebaseService';

interface AuthContextType {
  currentUser: User | null;
  users: User[];
  isAdmin: boolean;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  signUpUser: (email: string, password: string, username: string) => Promise<User | null>;

  toggleUserActivation: (userId: string) => void;
  removeUser: (userId: string) => void;
  loginScreenConfig: LoginScreenConfig;
  updateLoginScreenConfig: (newConfig: Partial<LoginScreenConfig>) => void;
  latestFeaturesConfig: LatestFeaturesConfig;
  updateLatestFeaturesConfig: (newConfig: Partial<LatestFeaturesConfig>) => void;
  adminMessages: AdminMessage[];
  sendMessageToAdmin: (messageData: Omit<AdminMessage, 'id' | 'timestamp' | 'isRead'>) => Promise<void>;
  markMessageAsRead: (messageId: string) => void;
  deleteAdminMessage: (messageId: string) => void;
  updateUserProfile: (userId: string, updates: { bio?: string; avatarUrl?: string }) => void;
  migrateUsersForFollowSystem: () => Promise<{ updated: number; total: number }>;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [users, setUsers] = useState<User[]>([]);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const [loginScreenConfig, setLoginScreenConfig] = useState<LoginScreenConfig>(DEFAULT_LOGIN_SCREEN_CONFIG);
  const [latestFeaturesConfig, setLatestFeaturesConfig] = useState<LatestFeaturesConfig>(DEFAULT_LATEST_FEATURES_CONFIG);

  const [adminMessages, setAdminMessages] = useState<AdminMessage[]>([]);

  const isAdmin = useMemo(() => currentUser?.isAdmin === true, [currentUser]);

  // Firebase auth state listener
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser: FirebaseUser | null) => {
      if (firebaseUser) {
        try {
          const userData = await getUserById(firebaseUser.uid);

          // Set user data if it exists (active users can log in)
          if (userData) {
            setCurrentUser(userData);
          } else {
            // User data doesn't exist, sign them out
            await logOut();
            setCurrentUser(null);
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
          setCurrentUser(null);
        }
      } else {
        setCurrentUser(null);
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  // Load login screen config and latest features config
  useEffect(() => {
    const loadConfigs = async () => {
      try {
        const [loginConfig, featuresConfig] = await Promise.all([
          getLoginScreenConfig(),
          getLatestFeaturesConfig()
        ]);

        if (loginConfig) {
          setLoginScreenConfig(loginConfig);
        }

        if (featuresConfig) {
          setLatestFeaturesConfig(featuresConfig);
        }
      } catch (error) {
        console.error('Error loading configs:', error);
      }
    };
    loadConfigs();
  }, []);

  // Load users for all authenticated users, admin messages for admin only
  useEffect(() => {
    if (currentUser) {
      const loadUserData = async () => {
        try {
          if (isAdmin) {
            // Admin can see all users and admin messages
            const [allUsers, messages] = await Promise.all([
              getAllUsers(),
              getAllAdminMessages()
            ]);
            setUsers(allUsers);
            setAdminMessages(messages);
          } else {
            // Regular users can see active users only
            const activeUsers = await getActiveUsers();
            setUsers(activeUsers);
          }
        } catch (error) {
          console.error('Error loading user data:', error);
        }
      };
      loadUserData();
    } else {
      // Clear users when not authenticated
      setUsers([]);
      setAdminMessages([]);
    }
  }, [currentUser, isAdmin]);

  const login = useCallback(async (email: string, password: string): Promise<void> => {
    try {
      await signIn(email, password);
      // The auth state listener will handle setting the current user
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }, []);

  const logout = useCallback(async (): Promise<void> => {
    try {
      await logOut();
      // The auth state listener will handle clearing the current user
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }, []);

  const signUpUser = useCallback(async (email: string, password: string, username: string): Promise<User | null> => {
    try {
      const newUser = await firebaseSignUp(email, password, username);

      // Refresh users list for admin
      if (isAdmin) {
        const allUsers = await getAllUsers();
        setUsers(allUsers);
      }

      return newUser;
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    }
  }, [isAdmin]);

  const toggleUserActivation = useCallback(async (userId: string) => {
    try {
      const user = users.find(u => u.id === userId);
      if (!user) return;

      // Prevent deactivating admin accounts
      if (user.isAdmin) {
        alert("Cannot deactivate admin accounts.");
        return;
      }

      await updateUser(userId, {
        isActive: !user.isActive,
        isPendingApproval: user.isActive ? false : user.isPendingApproval
      });

      setUsers(prevUsers =>
        prevUsers.map(u =>
          u.id === userId ? { ...u, isActive: !u.isActive, isPendingApproval: u.isActive ? false : u.isPendingApproval } : u
        )
      );

      if (currentUser?.id === userId && !user.isActive) {
        await logout();
      }
    } catch (error) {
      console.error('Error toggling user activation:', error);
      throw error;
    }
  }, [currentUser, users, logout]);

  const removeUser = useCallback(async (userId: string) => {
    try {
      const user = users.find(u => u.id === userId);
      if (!user) return;

      // Prevent removing admin accounts
      if (user.isAdmin) {
        alert("Cannot remove admin accounts.");
        return;
      }

      // Delete user from Firestore database (this will also clean up all associated data)
      await deleteUser(userId);

      // Update local state to remove the user
      setUsers(prevUsers => prevUsers.filter(u => u.id !== userId));

      // If the deleted user is the current user, log them out
      if (currentUser?.id === userId) {
        await logout();
      }

      // Show success message with reminder about authentication cleanup
      alert(`✅ Database record for user "${user.username}" has been successfully deleted.\n\n⚠️ REMINDER: You need to manually delete this user from Firebase Authentication in the Firebase Console.`);

      console.log(`User ${user.username} (${userId}) has been permanently deleted from Firestore.`);
    } catch (error) {
      console.error('Error removing user:', error);
      alert(`Failed to delete user: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    }
  }, [currentUser, users, logout]);

  const updateLoginScreenConfig = useCallback(async (newConfig: Partial<LoginScreenConfig>) => {
    try {
      const updated = { ...loginScreenConfig, ...newConfig };
      await firebaseUpdateLoginScreenConfig(updated);
      setLoginScreenConfig(updated);
    } catch (error) {
      console.error("Error updating login screen config:", error);
      throw error;
    }
  }, [loginScreenConfig]);

  const updateLatestFeaturesConfig = useCallback(async (newConfig: Partial<LatestFeaturesConfig>) => {
    try {
      const updated = { ...latestFeaturesConfig, ...newConfig };
      await firebaseUpdateLatestFeaturesConfig(updated);
      setLatestFeaturesConfig(updated);
    } catch (error) {
      console.error("Error updating latest features config:", error);
      throw error;
    }
  }, [latestFeaturesConfig]);

  const sendMessageToAdmin = useCallback(async (messageData: Omit<AdminMessage, 'id' | 'timestamp' | 'isRead'>) => {
    try {
      await createAdminMessage(messageData);
      // Refresh admin messages if current user is admin
      if (isAdmin) {
        const messages = await getAllAdminMessages();
        setAdminMessages(messages);
      }
    } catch (error) {
      console.error("Error sending message to admin:", error);
      throw error;
    }
  }, [isAdmin]);

  const markMessageAsRead = useCallback(async (messageId: string) => {
    try {
      await markAdminMessageAsRead(messageId);
      setAdminMessages(prevMessages =>
        prevMessages.map(message =>
          message.id === messageId ? { ...message, isRead: true } : message
        )
      );
    } catch (error) {
      console.error("Error marking message as read:", error);
      throw error;
    }
  }, []);

  const deleteAdminMessage = useCallback(async (messageId: string) => {
    try {
      await firebaseDeleteAdminMessage(messageId);
      setAdminMessages(prevMessages => prevMessages.filter(message => message.id !== messageId));
    } catch (error) {
      console.error("Error deleting admin message:", error);
      throw error;
    }
  }, []);

  const updateUserProfile = useCallback(async (userId: string, updates: { bio?: string; avatarUrl?: string }) => {
    try {
      await updateUser(userId, updates);

      setUsers(prevUsers =>
        prevUsers.map(user =>
          user.id === userId
            ? { ...user, ...updates }
            : user
        )
      );

      // Update current user if it's the same user
      setCurrentUser(prevUser =>
        prevUser && prevUser.id === userId
          ? { ...prevUser, ...updates }
          : prevUser
      );
    } catch (error) {
      console.error("Error updating user profile:", error);
      throw error;
    }
  }, []);

  const migrateUsersForFollowSystemCallback = useCallback(async (): Promise<{ updated: number; total: number }> => {
    try {
      const result = await migrateUsersForFollowSystem();

      // Refresh users list after migration
      if (isAdmin) {
        const allUsers = await getAllUsers();
        setUsers(allUsers);
      }

      return result;
    } catch (error) {
      console.error("Error migrating users for follow system:", error);
      throw error;
    }
  }, [isAdmin]);

  const contextValue = useMemo(() => ({
    currentUser,
    users,
    isAdmin,
    loading,
    login,
    logout,
    signUpUser,
    toggleUserActivation,
    removeUser,
    loginScreenConfig,
    updateLoginScreenConfig,
    latestFeaturesConfig,
    updateLatestFeaturesConfig,
    adminMessages,
    sendMessageToAdmin,
    markMessageAsRead,
    deleteAdminMessage,
    updateUserProfile,
    migrateUsersForFollowSystem: migrateUsersForFollowSystemCallback,
  }), [currentUser, users, isAdmin, loading, login, logout, signUpUser, toggleUserActivation, removeUser, loginScreenConfig, updateLoginScreenConfig, latestFeaturesConfig, updateLatestFeaturesConfig, adminMessages, sendMessageToAdmin, markMessageAsRead, deleteAdminMessage, updateUserProfile, migrateUsersForFollowSystemCallback]);

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};
